'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { UsageDashboard, UsageStatsCard, LimitWarning, QuickUsageIndicator } from '@/components/UsageDisplay'
import SubscriptionBadge from '@/components/SubscriptionBadge'
import { UserUsageStats } from '@/types/subscription.types'
import { SubscriptionTier } from '@/types/schema.types'

export default function TestUsageSystem() {
  const { data: session, status } = useSession()
  const [usageStats, setUsageStats] = useState<UserUsageStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<string[]>([])

  const fetchUsageStats = async () => {
    if (!session?.user?.id) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/v1/users/me/usage')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      setUsageStats(data.usage)
      addTestResult('✅ Successfully fetched usage statistics')
    } catch (err: any) {
      setError(err.message)
      addTestResult(`❌ Failed to fetch usage stats: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testSubscriptionAPI = async () => {
    if (!session?.user?.id) return

    try {
      const response = await fetch('/api/v1/users/me/subscription')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      addTestResult('✅ Subscription API working with usage data')
      addTestResult(`📊 Current tier: ${data.subscription_tier}`)
      addTestResult(`📈 Worlds: ${data.usage.worlds.current}/${data.usage.worlds.limit}`)
      addTestResult(`🤖 AI Messages: ${data.usage.ai_messages_daily.current}/${data.usage.ai_messages_daily.limit}`)
    } catch (err: any) {
      addTestResult(`❌ Subscription API test failed: ${err.message}`)
    }
  }

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`])
  }

  const runAllTests = async () => {
    setTestResults([])
    addTestResult('🚀 Starting usage system tests...')
    
    await fetchUsageStats()
    await testSubscriptionAPI()
    
    addTestResult('✅ All tests completed')
  }

  useEffect(() => {
    if (status === 'authenticated') {
      fetchUsageStats()
    }
  }, [status])

  if (status === 'loading') {
    return <div className="container mx-auto py-8 px-4">Loading...</div>
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-medieval text-primary mb-4">Authentication Required</h1>
          <p className="text-text-secondary">Please sign in to test the usage tracking system.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="bg-background-darker border border-gray-800 rounded-lg p-6">
        <h1 className="text-2xl font-medieval text-primary mb-6">Usage Tracking System Test</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Usage Display */}
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-medieval text-primary mb-4">Usage Dashboard</h2>
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="loading-dots">
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                </div>
              ) : usageStats ? (
                <UsageDashboard
                  worldsUsage={usageStats.worlds}
                  aiMessagesUsage={usageStats.ai_messages_daily}
                  subscriptionTier={usageStats.subscription_tier}
                />
              ) : (
                <div className="text-center py-8 text-text-secondary">
                  No usage data available
                </div>
              )}
            </div>

            {/* Individual Components Test */}
            {usageStats && (
              <div className="space-y-4">
                <h3 className="text-md font-medieval text-primary">Individual Components</h3>
                
                <UsageStatsCard
                  title="Worlds Test"
                  usage={usageStats.worlds}
                  limitType="worlds"
                  icon="🌍"
                />

                <div className="p-4 bg-background border border-gray-800 rounded-lg">
                  <h4 className="font-medium mb-2">Quick Usage Indicators</h4>
                  <div className="space-y-2">
                    <QuickUsageIndicator usage={usageStats.worlds} limitType="worlds" />
                    <QuickUsageIndicator usage={usageStats.ai_messages_daily} limitType="ai_messages" />
                    <QuickUsageIndicator usage={usageStats.worlds} limitType="worlds" compact />
                  </div>
                </div>

                {/* Limit Warning Test */}
                {usageStats.worlds.remaining === 0 && (
                  <LimitWarning
                    limitType="worlds"
                    current={usageStats.worlds.current}
                    limit={usageStats.worlds.limit}
                    tier={usageStats.subscription_tier}
                  />
                )}
              </div>
            )}
          </div>

          {/* Right Column - Test Controls & Results */}
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-medieval text-primary mb-4">System Tests</h2>
              
              <div className="space-y-3">
                <button
                  onClick={runAllTests}
                  className="fantasy-button w-full"
                >
                  Run All Tests
                </button>
                
                <button
                  onClick={fetchUsageStats}
                  disabled={loading}
                  className="w-full px-4 py-2 bg-primary/20 text-primary border border-primary/30 rounded hover:bg-primary/30 transition-colors disabled:opacity-50"
                >
                  {loading ? 'Loading...' : 'Refresh Usage Stats'}
                </button>
                
                <button
                  onClick={testSubscriptionAPI}
                  className="w-full px-4 py-2 bg-accent/20 text-accent border border-accent/30 rounded hover:bg-accent/30 transition-colors"
                >
                  Test Subscription API
                </button>
              </div>
            </div>

            {/* Test Results */}
            <div>
              <h3 className="text-md font-medieval text-primary mb-3">Test Results</h3>
              <div className="bg-background border border-gray-800 rounded-lg p-4 max-h-64 overflow-y-auto">
                {testResults.length > 0 ? (
                  <div className="space-y-1">
                    {testResults.map((result, index) => (
                      <div key={index} className="text-sm font-mono text-text-secondary">
                        {result}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-text-secondary text-sm">No test results yet</div>
                )}
              </div>
            </div>

            {/* Current Session Info */}
            <div>
              <h3 className="text-md font-medieval text-primary mb-3">Session Info</h3>
              <div className="bg-background border border-gray-800 rounded-lg p-4">
                <div className="space-y-2 text-sm">
                  <div><strong>User ID:</strong> {session?.user?.id}</div>
                  <div><strong>Email:</strong> {session?.user?.email}</div>
                  <div>
                    <strong>Subscription:</strong>{' '}
                    {session?.user?.subscription_tier ? (
                      <SubscriptionBadge tier={session.user.subscription_tier} size="sm" />
                    ) : (
                      'Not available'
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                <h3 className="text-red-400 font-medium mb-2">Error</h3>
                <p className="text-red-300 text-sm">{error}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
