import { NextRequest, NextResponse } from 'next/server';
import { checkUsageLimit, enforceUsageLimit, incrementUsage } from './usage-tracking.service';
import { LimitType, SubscriptionLimitError } from '@/types/subscription.types';

/**
 * Middleware to check subscription limits before allowing actions
 */
export async function withSubscriptionLimit(
  userId: string,
  limitType: LimitType,
  worldId?: string
) {
  return async function middleware(handler: Function) {
    try {
      // Check if the action is allowed
      await enforceUsageLimit({
        userId,
        limitType,
        worldId,
        increment: false // Don't increment yet, just check
      });

      // If allowed, execute the handler
      const result = await handler();

      // If handler was successful, increment the usage
      if (result && !result.error) {
        await incrementUsage(userId, limitType, worldId);
      }

      return result;
    } catch (error) {
      if (error instanceof SubscriptionLimitError) {
        return {
          error: error.message,
          code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
          details: {
            limitType: error.limitType,
            current: error.current,
            limit: error.limit,
            tier: error.tier
          }
        };
      }
      throw error;
    }
  };
}

/**
 * Helper function to create limit-aware API responses
 */
export function createLimitResponse(error: SubscriptionLimitError): NextResponse {
  return NextResponse.json(
    {
      error: error.message,
      code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
      details: {
        limitType: error.limitType,
        current: error.current,
        limit: error.limit,
        tier: error.tier,
        upgradeRequired: true
      }
    },
    { status: 429 } // Too Many Requests
  );
}

/**
 * Wrapper for API routes that need subscription limit checking
 */
export function withSubscriptionLimitAPI(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>,
  getLimitConfig: (request: NextRequest, context: any) => Promise<{
    userId: string;
    limitType: LimitType;
    worldId?: string;
  }>
) {
  return async function wrappedHandler(request: NextRequest, context: any): Promise<NextResponse> {
    try {
      const { userId, limitType, worldId } = await getLimitConfig(request, context);

      // Check if the action is allowed
      const limitCheck = await checkUsageLimit({
        userId,
        limitType,
        worldId,
        increment: false
      });

      if (!limitCheck.allowed) {
        return NextResponse.json(
          {
            error: `${limitType} limit exceeded`,
            code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
            details: {
              limitType,
              current: limitCheck.current,
              limit: limitCheck.limit,
              remaining: limitCheck.remaining,
              upgradeRequired: true
            }
          },
          { status: 429 }
        );
      }

      // Execute the original handler
      const response = await handler(request, context);

      // If successful (2xx status), increment usage
      if (response.ok) {
        try {
          await incrementUsage(userId, limitType, worldId);
        } catch (incrementError) {
          console.error('Error incrementing usage after successful operation:', incrementError);
          // Don't fail the request if increment fails, just log it
        }
      }

      return response;
    } catch (error) {
      console.error('Error in subscription limit middleware:', error);
      
      if (error instanceof SubscriptionLimitError) {
        return createLimitResponse(error);
      }

      // Re-throw other errors to be handled by the normal error handling
      throw error;
    }
  };
}

/**
 * Helper to check limits without enforcement (for UI display)
 */
export async function checkLimitsForDisplay(userId: string) {
  try {
    const [worldsCheck, aiMessagesCheck] = await Promise.all([
      checkUsageLimit({ userId, limitType: 'worlds' }),
      checkUsageLimit({ userId, limitType: 'ai_messages' })
    ]);

    return {
      worlds: worldsCheck,
      ai_messages: aiMessagesCheck
    };
  } catch (error) {
    console.error('Error checking limits for display:', error);
    return null;
  }
}

/**
 * Helper to check character limits for a specific world
 */
export async function checkCharacterLimitForWorld(userId: string, worldId: string) {
  try {
    return await checkUsageLimit({ 
      userId, 
      limitType: 'characters', 
      worldId 
    });
  } catch (error) {
    console.error('Error checking character limit for world:', error);
    return null;
  }
}

/**
 * Utility to format limit messages for UI
 */
export function formatLimitMessage(
  limitType: LimitType,
  current: number,
  limit: number,
  remaining: number
): string {
  if (limit === -1) {
    return `${current} ${limitType} (unlimited)`;
  }

  const percentage = (current / limit) * 100;
  
  if (remaining === 0) {
    return `Limit reached: ${current}/${limit} ${limitType}`;
  } else if (percentage >= 80) {
    return `${current}/${limit} ${limitType} (${remaining} remaining)`;
  } else {
    return `${current}/${limit} ${limitType}`;
  }
}

/**
 * Get upgrade suggestion based on limit type
 */
export function getUpgradeSuggestion(limitType: LimitType): string {
  switch (limitType) {
    case 'worlds':
      return 'Upgrade to Pro for 10 worlds or Pro+ for unlimited worlds';
    case 'characters':
      return 'Upgrade to Pro for 15 characters per world or Pro+ for unlimited characters';
    case 'ai_messages':
      return 'Upgrade to Pro for 200 daily messages or Pro+ for 1000 daily messages';
    default:
      return 'Upgrade your subscription for higher limits';
  }
}

/**
 * Check if user is approaching any limits (80% threshold)
 */
export async function checkApproachingLimits(userId: string) {
  try {
    const limits = await checkLimitsForDisplay(userId);
    if (!limits) return [];

    const approaching = [];

    // Check worlds limit
    if (limits.worlds.limit !== -1) {
      const worldsPercentage = (limits.worlds.current / limits.worlds.limit) * 100;
      if (worldsPercentage >= 80) {
        approaching.push({
          type: 'worlds' as LimitType,
          percentage: worldsPercentage,
          current: limits.worlds.current,
          limit: limits.worlds.limit,
          message: formatLimitMessage('worlds', limits.worlds.current, limits.worlds.limit, limits.worlds.remaining)
        });
      }
    }

    // Check AI messages limit
    if (limits.ai_messages.limit !== -1) {
      const messagesPercentage = (limits.ai_messages.current / limits.ai_messages.limit) * 100;
      if (messagesPercentage >= 80) {
        approaching.push({
          type: 'ai_messages' as LimitType,
          percentage: messagesPercentage,
          current: limits.ai_messages.current,
          limit: limits.ai_messages.limit,
          message: formatLimitMessage('ai_messages', limits.ai_messages.current, limits.ai_messages.limit, limits.ai_messages.remaining)
        });
      }
    }

    return approaching;
  } catch (error) {
    console.error('Error checking approaching limits:', error);
    return [];
  }
}
