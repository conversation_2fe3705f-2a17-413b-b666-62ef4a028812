# SoloQuest Enhanced Subscription System

## Overview

This enhanced subscription system provides centralized configuration management and simplified usage tracking with message-based limits for the SoloQuest application.

## Features Implemented

### 1. Centralized Subscription Configuration
- **File**: `src/config/subscription.config.ts`
- **Types**: `src/types/subscription.types.ts`
- Single source of truth for all subscription tiers and features
- Simplified structure with only message limits enforced
- TypeScript type safety throughout the system

### 2. Usage Tracking Database Schema
- **Migration**: `supabase/migrations/add_usage_tracking.sql`
- **Tables**: `user_usage` (simplified to track only messages)
- **Functions**: Monthly message count tracking with automatic reset
- Row Level Security (RLS) enabled for data protection

### 3. Usage Tracking Service Layer
- **File**: `src/lib/usage-tracking.service.ts`
- Functions for checking limits, incrementing usage, and retrieving stats
- Handles unlimited tiers and daily resets automatically

### 4. Limit Enforcement Middleware
- **File**: `src/lib/subscription-middleware.ts`
- API wrapper for automatic limit checking and enforcement
- Helper functions for UI display and error handling

### 5. Usage Display UI Components
- **File**: `src/components/UsageDisplay.tsx`
- Progress bars, usage cards, limit warnings, and dashboard
- Responsive design with visual indicators for approaching limits

## Database Schema

### user_usage Table
```sql
CREATE TABLE user_usage (
    id uuid PRIMARY KEY,
    user_id uuid REFERENCES users(id),
    messages_this_month integer DEFAULT 0,
    messages_reset_date date DEFAULT CURRENT_DATE,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
```

### Direct Counting Approach
- **Messages**: Tracked in `user_usage.messages_this_month` with monthly reset
- **Worlds**: Counted directly from `worlds` table (no limits enforced)
- **Characters**: Counted directly from `characters` table (no limits enforced)

## Subscription Tiers Configuration

### Free Tier ($0/month)
- 50 messages per month
- Basic World Settings
- Community Support

### Pro Tier ($9.99/month)
- 100 messages per month
- Edit Data
- Priority Support
- Export Data
- Early Access Features

### Pro+ Tier ($19.99/month)
- 500 messages per month
- Edit Data
- Priority Support
- Export Data
- Early Access Features
- Advanced AI Models

**Note**: Worlds and characters are now unlimited for all tiers - only message usage is tracked and limited.

## API Endpoints

### GET /api/v1/users/[userId]/subscription
Returns subscription tier, configuration, and usage statistics.

### PATCH /api/v1/users/[userId]/subscription
Updates subscription tier (admin only).

### GET /api/v1/users/[userId]/usage
Returns detailed usage statistics with optional world-specific data.

## Integration Examples

### Message API with Limit Enforcement
```typescript
import { withSubscriptionLimitAPI } from '@/lib/subscription-middleware';

export const POST = withSubscriptionLimitAPI(
  async (request: NextRequest) => {
    // Your AI message processing logic
    return NextResponse.json({ success: true });
  },
  async (request: NextRequest) => ({
    userId: session.user.id,
    limitType: 'ai_messages'
  })
);
```

### Manual Message Limit Checking
```typescript
import { checkUsageLimit, incrementUsage } from '@/lib/usage-tracking.service';

// Check limit before action
const limitCheck = await checkUsageLimit({
  userId: session.user.id,
  limitType: 'ai_messages'
});

if (!limitCheck.allowed) {
  return NextResponse.json({ error: 'Message limit exceeded' }, { status: 429 });
}

// Process AI message...

// Increment usage after success
await incrementUsage(session.user.id, 'ai_messages');
```

### Direct Database Function Call
```typescript
import { supabaseAdmin } from '@/lib/supabase-admin';

// Increment message count directly
const { data: newCount, error } = await supabaseAdmin.rpc(
  'increment_message_count',
  { p_user_id: userId }
);

if (error) {
  console.error('Error incrementing message count:', error);
} else {
  console.log('New message count:', newCount);
}
```

### World/Character Creation (No Limits)
```typescript
// Worlds and characters no longer have limits, but you can still track them
export async function POST(request: NextRequest) {
  const session = await requireUserSession(request);

  // Create world/character without limit checking
  // ... your creation logic ...

  return NextResponse.json({ success: true });
}
```

## UI Components Usage

### Usage Dashboard
```tsx
import { UsageDashboard } from '@/components/UsageDisplay';

<UsageDashboard
  worldsUsage={usageStats.worlds}
  aiMessagesUsage={usageStats.ai_messages_daily}
  subscriptionTier={user.subscription_tier}
/>
```

### Individual Usage Cards
```tsx
import { UsageStatsCard } from '@/components/UsageDisplay';

<UsageStatsCard
  title="Messages This Month"
  usage={messagesUsage}
  limitType="ai_messages"
  icon="🤖"
/>
```

### Limit Warnings (Messages Only)
```tsx
import { LimitWarning } from '@/components/UsageDisplay';

{isAtLimit && (
  <LimitWarning
    limitType="ai_messages"
    current={usage.current}
    limit={usage.limit}
    tier={user.subscription_tier}
    onUpgrade={() => router.push('/upgrade')}
  />
)}
```

### Subscription Badge
```tsx
import SubscriptionBadge from '@/components/SubscriptionBadge';

<SubscriptionBadge tier={user.subscription_tier} size="md" />
```

## Testing

### Test Pages
- `/test-usage-system` - Comprehensive system testing
- `/test-subscription-api` - API endpoint testing

### Manual Testing Steps
1. Run database migration: `supabase/migrations/add_usage_tracking.sql`
2. Visit test pages to verify functionality
3. Test message limit enforcement by reaching tier limits
4. Verify message count increments correctly
5. Test monthly reset for message counts

### Testing Message Increment
```sql
-- Test in Supabase SQL Editor
SELECT increment_message_count('your-user-id-here');

-- Check the result
SELECT * FROM user_usage WHERE user_id = 'your-user-id-here';
```

## How to Trigger Message Count Increment

### 1. Using the Usage Tracking Service (Recommended)
```typescript
import { incrementUsage } from '@/lib/usage-tracking.service';

// This will call increment_message_count internally
const newCount = await incrementUsage(userId, 'ai_messages');
```

### 2. Direct Database Function Call
```typescript
import { supabaseAdmin } from '@/lib/supabase-admin';

const { data: newCount, error } = await supabaseAdmin.rpc(
  'increment_message_count',
  { p_user_id: userId }
);
```

### 3. With Automatic Limit Checking
```typescript
import { checkUsageLimit } from '@/lib/usage-tracking.service';

const result = await checkUsageLimit({
  userId: session.user.id,
  limitType: 'ai_messages',
  increment: true // Automatically increments if allowed
});

if (!result.allowed) {
  return NextResponse.json({ error: 'Message limit exceeded' }, { status: 429 });
}
```

### 4. In API Routes
```typescript
// In your AI message API route
export async function POST(request: NextRequest) {
  const session = await requireUserSession(request);

  // Process AI message...

  // Increment count after successful processing
  const { data: newCount, error } = await supabaseAdmin.rpc(
    'increment_message_count',
    { p_user_id: session.user.id }
  );

  return NextResponse.json({ success: true, messageCount: newCount });
}
```

### When to Call increment_message_count
- ✅ After successful AI message processing
- ✅ When user generates AI content
- ✅ After AI API calls complete successfully
- ❌ Not for regular user messages (only AI-powered features)

## Error Handling

### SubscriptionLimitError
```typescript
try {
  await enforceUsageLimit({ userId, limitType: 'worlds' });
} catch (error) {
  if (error instanceof SubscriptionLimitError) {
    return NextResponse.json({
      error: error.message,
      code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
      details: {
        limitType: error.limitType,
        current: error.current,
        limit: error.limit,
        tier: error.tier
      }
    }, { status: 429 });
  }
}
```

## Configuration Management

### Updating Subscription Tiers
Edit `src/config/subscription.config.ts`:
```typescript
export const SUBSCRIPTION_TIERS = {
  free: {
    limits: {
      messages_per_month: 100, // Changed from 50
    },
    features: [
      "Basic World Settings",
      "Community Support",
      "New Feature Here", // Add new features
    ]
  }
};
```

### Adding New Features
1. Add feature string to the `features` array in tier configurations
2. Update UI display logic in components
3. Update comparison components automatically (they read from config)

### Adding New Tiers
1. Add new tier to `SubscriptionTier` type in `src/types/schema.types.ts`
2. Add configuration in `SUBSCRIPTION_TIERS` object
3. Update database constraints if needed

## Deployment Checklist

- [ ] Run database migration: `supabase/migrations/add_usage_tracking.sql`
- [ ] Update environment variables if needed
- [ ] Test message API endpoints with limit enforcement
- [ ] Verify UI components display correctly
- [ ] Test message limit enforcement (only enforced limit)
- [ ] Verify message count tracking accuracy
- [ ] Test subscription tier changes
- [ ] Validate error handling for message limits
- [ ] Test monthly reset functionality

## Future Enhancements

1. **Payment Integration**: Connect with Stripe/PayPal for automatic tier management
2. **Usage Analytics**: Add detailed message usage reporting and analytics
3. **Flexible Limits**: Allow custom message limits per user
4. **Usage Alerts**: Email notifications for approaching message limits
5. **Admin Dashboard**: Interface for managing user subscriptions
6. **Usage History**: Track message usage over time for insights
7. **Additional Limits**: Re-add world/character limits if needed in the future
8. **Usage Rollover**: Allow unused messages to roll over to next month

## Support

For questions or issues with the subscription system:
1. Check the test pages for system status
2. Review error logs for SubscriptionLimitError instances
3. Verify database migration was applied correctly
4. Ensure all required environment variables are set
