// Types generated from supabase/schema.sql

export type SubscriptionTier = 'free' | 'pro' | 'pro_plus';

export type User = {
  id: string;
  email: string;
  name?: string | null;
  avatar_url?: string | null;
  is_admin: boolean;
  subscription_tier: SubscriptionTier;
  created_at: string;
  updated_at: string;
};

export type World = {
  id: string;
  name?: string | null;
  description: Record<string, any>;
  dm_prompt?: string | null;
  notes: string;
  settings: Record<string, any>;
  user_id: string;
  created_at: string;
  updated_at: string;
};

export type Character = {
  id: string;
  type: 'player' | 'ai';
  name: string;
  gender: string;
  race: string;
  class: string;
  background: string;
  alignment: string;
  attributes: Record<string, any>;
  inventory: string;
  description: string;
  memories: string;
  user_id: string;
  world_id?: string | null;
  created_at: string;
  updated_at: string;
};

export type Campaign = {
  id: string;
  description?: string | null;
  world_id: string;
  created_at: string;
  updated_at: string;
};

export type Message = {
  id: number;
  content: string;
  sender: string;
  world_id?: string | null;
  user_id: string;
  character_id: string;
  created_at: string;
};

export type UserUsage = {
  id: string;
  user_id: string;
  messages_this_month: number;
  messages_reset_date: string;
  created_at: string;
  updated_at: string;
};