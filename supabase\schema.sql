
SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

CREATE EXTENSION IF NOT EXISTS "pgsodium";
COMMENT ON SCHEMA "public" IS 'standard public schema';
CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";
CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";
CREATE OR REPLACE FUNCTION "public"."get_current_user_id"() RETURNS "uuid"
    LANGUAGE "plpgsql" STABLE
    AS $$
DECLARE
  user_id_claim TEXT;
BEGIN
  -- Attempt to retrieve the 'user_id' claim. Returns NULL if not set or not a valid UUID.
  user_id_claim := current_setting('request.jwt.claims.user_id', true);
  IF user_id_claim IS NULL THEN
      RETURN NULL;
  END IF;
  BEGIN
    RETURN user_id_claim::UUID;
  EXCEPTION WHEN invalid_text_representation THEN
    -- Log or handle invalid format? For now, return NULL.
    RETURN NULL;
  END;
END;
$$;

ALTER FUNCTION "public"."get_current_user_id"() OWNER TO "postgres";
SET default_tablespace = '';
SET default_table_access_method = "heap";

CREATE TABLE IF NOT EXISTS "public"."campaigns" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "description" "text",
    "world_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);

ALTER TABLE "public"."campaigns" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."characters" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "type" "text" DEFAULT 'player'::"text" NOT NULL,
    "name" "text" NOT NULL,
    "gender" "text" NOT NULL,
    "race" "text" NOT NULL,
    "class" "text" NOT NULL,
    "background" "text" NOT NULL,
    "alignment" "text" NOT NULL,
    "attributes" "jsonb" DEFAULT '{}'::"jsonb",
    "inventory" "text" DEFAULT ''::"text",
    "description" "text" NOT NULL,
    "memories" "text" DEFAULT ''::"text",
    "user_id" "uuid" NOT NULL,
    "world_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "characters_type_check" CHECK (("type" = ANY (ARRAY['player'::"text", 'ai'::"text"])))
);

ALTER TABLE "public"."characters" OWNER TO "postgres";

CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" integer NOT NULL,
    "content" "text" NOT NULL,
    "sender" "text" NOT NULL,
    "world_id" "uuid",
    "user_id" "uuid",
    "character_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"()
);
ALTER TABLE "public"."messages" OWNER TO "postgres";

CREATE SEQUENCE IF NOT EXISTS "public"."messages_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "public"."messages_id_seq" OWNER TO "postgres";

ALTER SEQUENCE "public"."messages_id_seq" OWNED BY "public"."messages"."id";

CREATE TABLE IF NOT EXISTS "public"."n8n_chat_histories" (
    "id" integer NOT NULL,
    "session_id" character varying(255) NOT NULL,
    "message" "jsonb" NOT NULL
);

ALTER TABLE "public"."n8n_chat_histories" OWNER TO "postgres";

CREATE SEQUENCE IF NOT EXISTS "public"."n8n_chat_histories_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "public"."n8n_chat_histories_id_seq" OWNER TO "postgres";

ALTER SEQUENCE "public"."n8n_chat_histories_id_seq" OWNED BY "public"."n8n_chat_histories"."id";

CREATE TABLE IF NOT EXISTS "public"."running_world" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "world" "uuid" NOT NULL,
    "running" boolean DEFAULT false NOT NULL
);

ALTER TABLE "public"."running_world" OWNER TO "postgres";

ALTER TABLE "public"."running_world" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."running_world_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "email" "text" NOT NULL,
    "name" "text",
    "avatar_url" "text",
    "is_admin" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "theme" "text",
    "subscription_tier" "text" DEFAULT 'free'::"text" NOT NULL,
    CONSTRAINT "users_subscription_tier_check" CHECK (("subscription_tier" = ANY (ARRAY['free'::"text", 'pro'::"text", 'pro_plus'::"text"])))
);

ALTER TABLE "public"."users" OWNER TO "postgres";

COMMENT ON COLUMN "public"."users"."subscription_tier" IS 'User subscription tier: free, pro, or pro_plus';

CREATE TABLE IF NOT EXISTS "public"."worlds" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text",
    "description" "jsonb",
    "dm_prompt" "text",
    "notes" "text" DEFAULT ''::"text",
    "settings" "jsonb" DEFAULT '{}'::"jsonb",
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);

ALTER TABLE "public"."worlds" OWNER TO "postgres";

ALTER TABLE ONLY "public"."messages" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."messages_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."n8n_chat_histories" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."n8n_chat_histories_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."campaigns"
    ADD CONSTRAINT "campaigns_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."characters"
    ADD CONSTRAINT "characters_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."n8n_chat_histories"
    ADD CONSTRAINT "n8n_chat_histories_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."running_world"
    ADD CONSTRAINT "running_world_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."running_world"
    ADD CONSTRAINT "running_world_world_key" UNIQUE ("world");

ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");

ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."worlds"
    ADD CONSTRAINT "worlds_pkey" PRIMARY KEY ("id");

CREATE INDEX "idx_campaigns_world_id" ON "public"."campaigns" USING "btree" ("world_id");

CREATE INDEX "idx_characters_user_id" ON "public"."characters" USING "btree" ("user_id");

CREATE INDEX "idx_characters_world_id" ON "public"."characters" USING "btree" ("world_id");

CREATE INDEX "idx_messages_character_id" ON "public"."messages" USING "btree" ("character_id");

CREATE INDEX "idx_messages_created_at" ON "public"."messages" USING "btree" ("created_at");

CREATE INDEX "idx_messages_user_id" ON "public"."messages" USING "btree" ("user_id");

CREATE INDEX "idx_messages_world_id" ON "public"."messages" USING "btree" ("world_id");

CREATE INDEX "idx_users_email" ON "public"."users" USING "btree" ("email");

CREATE INDEX "idx_worlds_created_at" ON "public"."worlds" USING "btree" ("created_at");

CREATE INDEX "idx_worlds_user_id" ON "public"."worlds" USING "btree" ("user_id");

ALTER TABLE ONLY "public"."campaigns"
    ADD CONSTRAINT "campaigns_world_id_fkey" FOREIGN KEY ("world_id") REFERENCES "public"."worlds"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."characters"
    ADD CONSTRAINT "characters_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."characters"
    ADD CONSTRAINT "characters_world_id_fkey" FOREIGN KEY ("world_id") REFERENCES "public"."worlds"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_character_id_fkey" FOREIGN KEY ("character_id") REFERENCES "public"."characters"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_world_id_fkey" FOREIGN KEY ("world_id") REFERENCES "public"."worlds"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."running_world"
    ADD CONSTRAINT "running_world_world_fkey" FOREIGN KEY ("world") REFERENCES "public"."worlds"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."worlds"
    ADD CONSTRAINT "worlds_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;

CREATE POLICY "Allow realtime select for user messages" ON "public"."messages" FOR SELECT USING (((("auth"."uid"())::"text" = ("user_id")::"text") OR (EXISTS ( SELECT 1
   FROM "public"."worlds"
  WHERE (("worlds"."id" = "messages"."world_id") AND (("worlds"."user_id")::"text" = ("auth"."uid"())::"text"))))));

CREATE POLICY "Allow users to delete campaigns in their worlds" ON "public"."campaigns" FOR DELETE USING ((EXISTS ( SELECT 1
   FROM "public"."worlds"
  WHERE (("worlds"."id" = "campaigns"."world_id") AND (("worlds"."user_id")::"text" = ("auth"."uid"())::"text")))));

CREATE POLICY "Allow users to delete their own characters" ON "public"."characters" FOR DELETE USING ((("auth"."uid"())::"text" = ("user_id")::"text"));

CREATE POLICY "Allow users to delete their own messages" ON "public"."messages" FOR DELETE USING ((("auth"."uid"())::"text" = ("user_id")::"text"));
CREATE POLICY "Allow users to delete their own worlds" ON "public"."worlds" FOR DELETE USING ((("auth"."uid"())::"text" = ("user_id")::"text"));

CREATE POLICY "Allow users to select campaigns in their worlds" ON "public"."campaigns" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."worlds"
  WHERE (("worlds"."id" = "campaigns"."world_id") AND (("worlds"."user_id")::"text" = ("auth"."uid"())::"text")))));
CREATE POLICY "Allow users to select their own characters" ON "public"."characters" FOR SELECT USING ((("auth"."uid"())::"text" = ("user_id")::"text"));
CREATE POLICY "Allow users to select their own messages" ON "public"."messages" FOR SELECT USING ((("auth"."uid"())::"text" = ("user_id")::"text"));
CREATE POLICY "Allow users to select their own worlds" ON "public"."worlds" FOR SELECT USING ((("auth"."uid"())::"text" = ("user_id")::"text"));

ALTER TABLE "public"."campaigns" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."characters" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."n8n_chat_histories" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."running_world" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."worlds" ENABLE ROW LEVEL SECURITY;
ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";
ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."messages";
GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";

GRANT ALL ON FUNCTION "public"."get_current_user_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_current_user_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_current_user_id"() TO "service_role";
GRANT ALL ON TABLE "public"."campaigns" TO "anon";
GRANT ALL ON TABLE "public"."campaigns" TO "authenticated";
GRANT ALL ON TABLE "public"."campaigns" TO "service_role";
GRANT ALL ON TABLE "public"."characters" TO "anon";
GRANT ALL ON TABLE "public"."characters" TO "authenticated";
GRANT ALL ON TABLE "public"."characters" TO "service_role";
GRANT ALL ON TABLE "public"."messages" TO "anon";
GRANT ALL ON TABLE "public"."messages" TO "authenticated";
GRANT ALL ON TABLE "public"."messages" TO "service_role";
GRANT ALL ON SEQUENCE "public"."messages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."messages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."messages_id_seq" TO "service_role";
GRANT ALL ON TABLE "public"."n8n_chat_histories" TO "anon";
GRANT ALL ON TABLE "public"."n8n_chat_histories" TO "authenticated";
GRANT ALL ON TABLE "public"."n8n_chat_histories" TO "service_role";
GRANT ALL ON SEQUENCE "public"."n8n_chat_histories_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."n8n_chat_histories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."n8n_chat_histories_id_seq" TO "service_role";
GRANT ALL ON TABLE "public"."running_world" TO "anon";
GRANT ALL ON TABLE "public"."running_world" TO "authenticated";
GRANT ALL ON TABLE "public"."running_world" TO "service_role";
GRANT ALL ON SEQUENCE "public"."running_world_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."running_world_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."running_world_id_seq" TO "service_role";
GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";
GRANT ALL ON TABLE "public"."worlds" TO "anon";
GRANT ALL ON TABLE "public"."worlds" TO "authenticated";
GRANT ALL ON TABLE "public"."worlds" TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";
RESET ALL;