/**
 * EXAMPLE INTEGRATIONS FOR USAGE TRACKING
 * 
 * These examples show how to integrate usage tracking and limit enforcement
 * into your existing API endpoints. Copy and adapt these patterns for your
 * actual API routes.
 */

import { NextRequest, NextResponse } from 'next/server';
import { requireUserSession } from '@/lib/auth-helpers';
import { withSubscriptionLimitAPI } from '@/lib/subscription-middleware';
import { checkUsageLimit, incrementUsage } from '@/lib/usage-tracking.service';
import { SubscriptionLimitError } from '@/types/subscription.types';

// ============================================================================
// EXAMPLE 1: World Creation API with Usage Tracking
// ============================================================================

/**
 * Example: POST /api/v1/worlds
 * Creates a new world with world limit enforcement
 */
export const createWorldExample = withSubscriptionLimitAPI(
  async (request: NextRequest, context: any): Promise<NextResponse> => {
    const session = await requireUserSession(request);
    const body = await request.json();

    // Your existing world creation logic here
    const worldData = {
      name: body.name,
      description: body.description,
      user_id: session.user.id,
      // ... other world properties
    };

    // Create world in database (your existing logic)
    // const { data: newWorld, error } = await supabaseAdmin
    //   .from('worlds')
    //   .insert(worldData)
    //   .select()
    //   .single();

    // Return success response
    return NextResponse.json({
      // world: newWorld,
      message: 'World created successfully'
    });
  },
  // Configuration function that returns limit check parameters
  async (request: NextRequest, context: any) => {
    const session = await requireUserSession(request);
    return {
      userId: session.user.id,
      limitType: 'worlds' as const
    };
  }
);

// ============================================================================
// EXAMPLE 2: Character Creation API with Usage Tracking
// ============================================================================

/**
 * Example: POST /api/v1/characters
 * Creates a new character with character-per-world limit enforcement
 */
export const createCharacterExample = withSubscriptionLimitAPI(
  async (request: NextRequest, context: any): Promise<NextResponse> => {
    const session = await requireUserSession(request);
    const body = await request.json();

    // Your existing character creation logic here
    const characterData = {
      name: body.name,
      race: body.race,
      class: body.class,
      world_id: body.world_id,
      user_id: session.user.id,
      // ... other character properties
    };

    // Create character in database (your existing logic)
    // const { data: newCharacter, error } = await supabaseAdmin
    //   .from('characters')
    //   .insert(characterData)
    //   .select()
    //   .single();

    return NextResponse.json({
      // character: newCharacter,
      message: 'Character created successfully'
    });
  },
  // Configuration function that extracts worldId from request body
  async (request: NextRequest, context: any) => {
    const session = await requireUserSession(request);
    const body = await request.json();
    return {
      userId: session.user.id,
      limitType: 'characters' as const,
      worldId: body.world_id
    };
  }
);

// ============================================================================
// EXAMPLE 3: AI Message API with Usage Tracking
// ============================================================================

/**
 * Example: POST /api/v1/ai/message
 * Sends AI message with daily message limit enforcement
 */
export const sendAIMessageExample = withSubscriptionLimitAPI(
  async (request: NextRequest, context: any): Promise<NextResponse> => {
    const session = await requireUserSession(request);
    const body = await request.json();

    // Your existing AI message logic here
    const messageData = {
      content: body.message,
      user_id: session.user.id,
      world_id: body.world_id,
      // ... other message properties
    };

    // Process AI message (your existing logic)
    // const aiResponse = await processAIMessage(messageData);

    return NextResponse.json({
      // response: aiResponse,
      message: 'AI message processed successfully'
    });
  },
  async (request: NextRequest, context: any) => {
    const session = await requireUserSession(request);
    return {
      userId: session.user.id,
      limitType: 'ai_messages' as const
    };
  }
);

// ============================================================================
// EXAMPLE 4: Manual Usage Tracking (Alternative Approach)
// ============================================================================

/**
 * Example: Manual usage tracking for more complex scenarios
 * Use this approach when you need more control over the tracking logic
 */
export async function manualUsageTrackingExample(request: NextRequest) {
  try {
    const session = await requireUserSession(request);
    const body = await request.json();

    // Check limit before proceeding
    const limitCheck = await checkUsageLimit({
      userId: session.user.id,
      limitType: 'worlds',
      increment: false
    });

    if (!limitCheck.allowed) {
      return NextResponse.json(
        {
          error: 'World limit exceeded',
          details: {
            current: limitCheck.current,
            limit: limitCheck.limit,
            tier: 'free' // You'd get this from the user's subscription
          }
        },
        { status: 429 }
      );
    }

    // Proceed with your business logic
    // ... your existing world creation code ...

    // Increment usage after successful operation
    await incrementUsage(session.user.id, 'worlds');

    return NextResponse.json({ success: true });
  } catch (error) {
    if (error instanceof SubscriptionLimitError) {
      return NextResponse.json(
        {
          error: error.message,
          code: 'SUBSCRIPTION_LIMIT_EXCEEDED',
          details: {
            limitType: error.limitType,
            current: error.current,
            limit: error.limit,
            tier: error.tier
          }
        },
        { status: 429 }
      );
    }
    throw error;
  }
}

// ============================================================================
// EXAMPLE 5: Deletion APIs with Usage Decrement
// ============================================================================

/**
 * Example: DELETE /api/v1/worlds/[worldId]
 * Deletes a world and decrements usage counter
 */
export async function deleteWorldExample(
  request: NextRequest,
  { params }: { params: { worldId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const worldId = params.worldId;

    // Your existing deletion logic here
    // const { error } = await supabaseAdmin
    //   .from('worlds')
    //   .delete()
    //   .eq('id', worldId)
    //   .eq('user_id', session.user.id);

    // Decrement usage counter after successful deletion
    // await decrementUsage(session.user.id, 'worlds', worldId);

    return NextResponse.json({ message: 'World deleted successfully' });
  } catch (error) {
    console.error('Error deleting world:', error);
    return NextResponse.json(
      { error: 'Failed to delete world' },
      { status: 500 }
    );
  }
}

/**
 * Example: DELETE /api/v1/characters/[characterId]
 * Deletes a character and decrements usage counter
 */
export async function deleteCharacterExample(
  request: NextRequest,
  { params }: { params: { characterId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const characterId = params.characterId;

    // Get character info before deletion (to get world_id)
    // const { data: character } = await supabaseAdmin
    //   .from('characters')
    //   .select('world_id')
    //   .eq('id', characterId)
    //   .eq('user_id', session.user.id)
    //   .single();

    // Your existing deletion logic here
    // const { error } = await supabaseAdmin
    //   .from('characters')
    //   .delete()
    //   .eq('id', characterId)
    //   .eq('user_id', session.user.id);

    // Decrement usage counter after successful deletion
    // if (character?.world_id) {
    //   await decrementUsage(session.user.id, 'characters', character.world_id);
    // }

    return NextResponse.json({ message: 'Character deleted successfully' });
  } catch (error) {
    console.error('Error deleting character:', error);
    return NextResponse.json(
      { error: 'Failed to delete character' },
      { status: 500 }
    );
  }
}

// ============================================================================
// INTEGRATION CHECKLIST
// ============================================================================

/**
 * INTEGRATION CHECKLIST:
 * 
 * 1. For CREATE operations:
 *    - Use withSubscriptionLimitAPI wrapper OR manual checkUsageLimit
 *    - Increment usage after successful creation
 *    - Return 429 status with upgrade message on limit exceeded
 * 
 * 2. For DELETE operations:
 *    - Decrement usage after successful deletion
 *    - For characters, you need the world_id for proper decrement
 * 
 * 3. For UI components:
 *    - Show usage indicators before creation forms
 *    - Display limit warnings when approaching limits
 *    - Provide upgrade suggestions when limits are reached
 * 
 * 4. Error handling:
 *    - Catch SubscriptionLimitError for proper user feedback
 *    - Log usage tracking errors but don't fail the main operation
 * 
 * 5. Database migration:
 *    - Run the usage tracking migration SQL
 *    - Initialize usage records for existing users
 */
