'use client'

import { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { useSupabaseClient } from '@/lib/supabase-client'
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'

type Message = {
  id: number
  content: string
  sender: string
  world_id?: string
  user_id?: string
  character_id?: string
  created_at: string
}

export default function TestSubscriptionPage() {
  const { data: session, status } = useSession()
  const supabase = useSupabaseClient()

  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [subscriptionStatus, setSubscriptionStatus] = useState<'connected' | 'disconnected' | 'error'>('disconnected')
  const [realtimeLog, setRealtimeLog] = useState<string[]>([])
  const subscriptionRef = useRef<RealtimeChannel | null>(null)

  // Fetch initial messages and set up realtime subscription
  useEffect(() => {
    if (status !== 'authenticated' || !session?.user?.id) return

    // Check if we have a Supabase JWT token
    if (!session?.supabaseAccessToken) {
      console.error('No Supabase JWT token available in session')
      setError('Authentication error: No Supabase JWT token available')
      return
    }

    console.log('Setting up subscription with JWT token available:', !!session?.supabaseAccessToken)

    // Cleanup any existing subscription first
    if (subscriptionRef.current) {
      console.log('Cleaning up existing subscription before creating a new one')
      supabase.removeChannel(subscriptionRef.current)
      subscriptionRef.current = null
    }

    async function setupRealtimeAndFetchMessages() {
      try {
        setLoading(true)
        setError(null)

        // Fetch initial messages
        const { data, error } = await supabase
          .from('messages')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(20)

        if (error) {
          console.error('Error fetching messages:', error)
          setError(`Failed to fetch messages: ${error.message}`)
          return
        }

        setMessages(data || [])

        // Set up realtime subscription
        console.log(`Setting up subscription with filter: user_id=eq.${session?.user?.id}`)

        // Create a unique channel name for this user to avoid conflicts
        const channelName = `messages-changes-${session?.user?.id?.substring(0, 8)}`
        console.log(`Creating channel: ${channelName}`)

        // Log the JWT token length to verify it's being used (don't log the full token for security)
        console.log('JWT token length:', session?.supabaseAccessToken?.length || 0)
        console.log('JWT token first 10 chars:', session?.supabaseAccessToken?.substring(0, 10) || 'none')

        // Check if realtime client has the access token set
        if (supabase.realtime) {
          console.log('Realtime client available')
          // Call the accessToken function to check if it returns the token
          // Use optional chaining to safely access the method
          const getToken = supabase.realtime.accessToken
          if (typeof getToken === 'function') {
            getToken().then(token => {
              console.log('Realtime access token available:', !!token)
              console.log('Realtime token length:', token?.length || 0)
            }).catch(err => {
              console.error('Error getting realtime token:', err)
            })
          } else {
            console.warn('accessToken is not a function')
          }
        } else {
          console.warn('Realtime client not available')
        }

        // Set up the channel with JWT token
        const subscription = supabase
          .channel(channelName, {
            config: {
              broadcast: { self: true },
              presence: { key: session?.user?.id || 'anonymous' }
            }
          })

        // No need to manually set the token anymore
        // Our improved supabase-client.ts handles token refreshing automatically

        // Set up the postgres changes listener
        subscription.on('postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'messages',
              // Try without filter to see if we get any messages at all
              // If this works but filtered doesn't, it's an RLS issue
              // filter: `user_id=eq.${session?.user?.id}`
            },
            (payload: RealtimePostgresChangesPayload<any>) => {
              const payloadNew = payload.new as Message | null;
              const payloadOld = payload.old as Message | null;
              const newLog = `${new Date().toLocaleTimeString()}: ${payload.eventType} - ID: ${payloadNew?.id || payloadOld?.id || 'unknown'}`
              setRealtimeLog(prev => [newLog, ...prev].slice(0, 50))

              if (payload.eventType === 'INSERT') {
                // Add new message to the list
                setMessages(prev => [payload.new as Message, ...prev])
              } else if (payload.eventType === 'UPDATE') {
                // Update existing message
                setMessages(prev =>
                  prev.map(msg => msg.id === (payload.new as Message).id ? payload.new as Message : msg)
                )
              } else if (payload.eventType === 'DELETE') {
                // Remove deleted message
                setMessages(prev =>
                  prev.filter(msg => msg.id !== (payload.old as Message).id)
                )
              }
            }
          )
          .subscribe((status: string, err?: any) => {
            console.log('Subscription status:', status, err)
            if (status === 'SUBSCRIBED') {
              setSubscriptionStatus('connected')
              setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: Subscription connected`, ...prev])

              // Log successful subscription details
              console.log('Subscription details:', {
                channel: subscription.topic,
                status,
                userId: session?.user?.id,
                hasToken: !!session?.supabaseAccessToken
              })
            } else if (status === 'CHANNEL_ERROR') {
              setSubscriptionStatus('error')
              setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: Subscription error: ${err?.message || 'Unknown error'}`, ...prev])
              setError(`Failed to connect to realtime subscription: ${err?.message || 'Unknown error'}`)
              console.error('Subscription error:', err)
            } else {
              setSubscriptionStatus('disconnected')
              setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: Subscription status: ${status}`, ...prev])
            }
          })

        // Store the subscription in the ref
        subscriptionRef.current = subscription

      } catch (err: any) {
        console.error('Exception setting up realtime:', err)
        setError(`Error: ${err.message}`)
        setSubscriptionStatus('error')
      } finally {
        setLoading(false)
      }
    }

    setupRealtimeAndFetchMessages()

    // Cleanup subscription on unmount
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current)
        console.log('Subscription removed on cleanup')
        subscriptionRef.current = null
      }
    }
  }, [session?.user?.id, status]) // Only depend on user ID and auth status

  // Test subscription by inserting a message directly
  const testSubscription = async () => {
    if (!session?.user?.id) {
      setError('You must be signed in to test the subscription')
      return
    }

    try {
      setError(null)
      setSuccess(null)

      // Insert a test message
      const { data, error } = await supabase
        .from('messages')
        .insert({
          content: `Test message from subscription test at ${new Date().toLocaleTimeString()}`,
          sender: session.user.name || 'Anonymous',
          user_id: session.user.id
        })
        .select()

      if (error) {
        console.error('Error creating test message:', error)
        setError(`Failed to create test message: ${error.message}`)
        return
      }

      // Success
      console.log('Test message created:', data)
      setSuccess(`Test message created with ID: ${data[0]?.id}. Check if it appears in the messages list below.`)
      setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: Test message created with ID: ${data[0]?.id}`, ...prev])
    } catch (err: any) {
      console.error('Exception creating test message:', err)
      setError(`Error: ${err.message}`)
    }
  }

  // Test RLS access directly
  const testRlsAccess = async () => {
    if (!session?.user?.id) {
      setError('You must be signed in to test RLS')
      return
    }

    try {
      setError(null)
      setSuccess(null)

      // Attempt to fetch messages directly
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('user_id', session.user.id)
        .limit(5)

      if (error) {
        console.error('RLS test error:', error)
        setError(`RLS test failed: ${error.message}`)
        setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: RLS test failed: ${error.message}`, ...prev])
        return
      }

      // Success
      console.log('RLS test successful:', data)
      setSuccess(`RLS test successful: Retrieved ${data.length} messages`)
      setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: RLS test successful: Retrieved ${data.length} messages`, ...prev])
    } catch (err: any) {
      console.error('Exception during RLS test:', err)
      setError(`Error during RLS test: ${err.message}`)
    }
  }

  // Create a new message
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!session?.user?.id) {
      setError('You must be signed in to create messages')
      return
    }

    if (!newMessage.trim()) {
      setError('Message cannot be empty')
      return
    }

    try {
      setError(null)
      setSuccess(null)

      // Insert the new message using the authenticated client
      const { data, error } = await supabase
        .from('messages')
        .insert({
          content: newMessage,
          sender: session.user.name || 'Anonymous',
          user_id: session.user.id
        })
        .select()

      if (error) {
        console.error('Error creating message:', error)
        setError(`Failed to create message: ${error.message}`)
        return
      }

      // Verify RLS - attempt to fetch the message we just created
      if (data && data[0]?.id) {
        console.log(`Verifying RLS: Trying to fetch message ${data[0].id}`)
        const { data: fetchedData, error: fetchError } = await supabase
          .from('messages')
          .select('*')
          .eq('id', data[0].id)
          .single()

        if (fetchError) {
          console.error('RLS verification error:', fetchError)
          setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: RLS verification failed: ${fetchError.message}`, ...prev])
        } else {
          console.log('RLS verification successful:', fetchedData)
          setRealtimeLog(prev => [`${new Date().toLocaleTimeString()}: RLS verification successful for message ${data[0].id}`, ...prev])
        }
      }

      // Clear the input field and show success message
      // (we don't need to update messages array as the subscription will do that)
      setNewMessage('')
      setSuccess('Message created successfully')
    } catch (err: any) {
      console.error('Exception creating message:', err)
      setError(`Error: ${err.message}`)
    }
  }

  if (status === 'loading') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Realtime Subscription Test</h1>
        <div className="flex items-center justify-center h-40">
          <p className="text-gray-500">Loading session...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Realtime Subscription Test</h1>
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <p className="text-yellow-700">You must be signed in to view this page.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-gradient-to-b from-blue-900 to-indigo-900 text-white">
      <h1 className="text-2xl font-bold mb-6 text-white">Realtime Subscription Test</h1>

      {/* Subscription Status */}
      <div className="mb-8 bg-blue-800 p-4 rounded-md border border-blue-600">
        <h2 className="text-lg font-semibold mb-2 text-white">Subscription Status</h2>
        <div className="flex items-center mb-4">
          <div className={`w-3 h-3 rounded-full mr-2 ${
            subscriptionStatus === 'connected' ? 'bg-green-500' :
            subscriptionStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'
          }`}></div>
          <span className="capitalize">{subscriptionStatus}</span>
        </div>

        <div className="flex space-x-4">
          <button
            onClick={testRlsAccess}
            className="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Test Direct RLS Access
          </button>
          <button
            onClick={testSubscription}
            className="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Test Subscription
          </button>
        </div>
      </div>

      {/* Message creation form */}
      <div className="mb-8 bg-blue-800 p-4 rounded-md border border-blue-600">
        <h2 className="text-lg font-semibold mb-4 text-white">Create New Message</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-blue-200 mb-1">
              Message Content
            </label>
            <textarea
              id="message"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              className="w-full px-3 py-2 border border-blue-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-blue-900 text-white"
              rows={3}
              placeholder="Enter a message..."
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Create Message
          </button>
        </form>

        {/* Status messages */}
        {error && (
          <div className="mt-4 bg-red-800 border border-red-600 text-red-200 p-3 rounded">
            {error}
          </div>
        )}
        {success && (
          <div className="mt-4 bg-green-800 border border-green-600 text-green-200 p-3 rounded">
            {success}
          </div>
        )}
      </div>

      {/* Realtime Log */}
      <div className="mb-8 bg-blue-800 p-4 rounded-md border border-blue-600">
        <h2 className="text-lg font-semibold mb-2 text-white">Realtime Events Log</h2>
        <div className="bg-blue-900 border border-blue-700 rounded-md p-3 h-40 overflow-auto">
          {realtimeLog.length > 0 ? (
            <ul className="space-y-1 text-xs font-mono">
              {realtimeLog.map((log, index) => (
                <li key={index} className="text-blue-200">{log}</li>
              ))}
            </ul>
          ) : (
            <p className="text-blue-300 text-center italic">No events yet</p>
          )}
        </div>
      </div>

      {/* Messages display */}
      <div className="bg-blue-800 p-4 rounded-md border border-blue-600">
        <h2 className="text-lg font-semibold mb-4 text-white">Messages (Realtime)</h2>

        {loading && messages.length === 0 ? (
          <div className="flex items-center justify-center h-40">
            <p className="text-blue-200">Loading messages...</p>
          </div>
        ) : messages.length > 0 ? (
          <ul className="space-y-4">
            {messages.map((message) => (
              <li
                key={message.id}
                className="border border-blue-600 rounded-md p-4 bg-blue-700 shadow-sm"
              >
                <div className="flex justify-between mb-2">
                  <span className="font-semibold text-white">{message.sender}</span>
                  <span className="text-sm text-blue-300">
                    {new Date(message.created_at).toLocaleString()}
                  </span>
                </div>
                <p className="text-white">{message.content}</p>
                <div className="mt-2 text-xs text-blue-300">
                  <p>ID: {message.id}</p>
                  <p>User ID: {message.user_id}</p>
                  {message.world_id && <p>World ID: {message.world_id}</p>}
                  {message.character_id && <p>Character ID: {message.character_id}</p>}
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="bg-blue-700 border border-blue-600 rounded-md p-8 text-center">
            <p className="text-blue-200">No messages found. Create your first message above!</p>
          </div>
        )}
      </div>
    </div>
  )
}
