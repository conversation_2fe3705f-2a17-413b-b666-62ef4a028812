{
  "extends": [
    "next/core-web-vitals",
    "plugin:import/recommended"
  ],
  "plugins": [
    "react",
    "jsx-a11y",
    "import"
  ],
  "rules": {
    "no-unused-vars": "warn",
    "import/no-unused-modules": "warn",
    "react-hooks/exhaustive-deps": "warn",
    "react/no-unescaped-entities": "warn",
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "warn",
    "react/jsx-key": "error",
    "react/no-array-index-key": "warn",
    "jsx-a11y/alt-text": "warn",
    "jsx-a11y/anchor-is-valid": "warn",
    "jsx-a11y/click-events-have-key-events": "warn",
    "jsx-a11y/no-static-element-interactions": "warn",
    "import/order": [
      "warn",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "newlines-between": "always"
      }
    ],
    // "no-console": "warn",
    "no-debugger": "warn",
    "prefer-const": "warn",
    "no-var": "error"
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  }
} 