import { NextRequest, NextResponse } from 'next/server';
import { requireUserSession, requireSelfOrAdmin } from '@/lib/auth-helpers';
import { getUserUsageStats } from '@/lib/usage-tracking.service';
import { checkCharacterLimitForWorld } from '@/lib/subscription-middleware';

/**
 * GET /api/v1/users/[userId]/usage
 * Get user's usage statistics and limits
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Get URL parameters
    const { searchParams } = new URL(request.url);
    const worldId = searchParams.get('worldId');

    // Get basic usage stats
    const usageStats = await getUserUsageStats(targetUserId);

    // If worldId is provided, also get character limit for that world
    let characterUsage = null;
    if (worldId) {
      characterUsage = await checkCharacterLimitForWorld(targetUserId, worldId);
    }

    return NextResponse.json({
      user_id: targetUserId,
      subscription_tier: usageStats.subscription_tier,
      usage: {
        worlds: usageStats.worlds,
        ai_messages_daily: usageStats.ai_messages_daily,
        ...(characterUsage && { characters_in_world: characterUsage })
      }
    });
  } catch (error) {
    console.error('Error getting user usage:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
