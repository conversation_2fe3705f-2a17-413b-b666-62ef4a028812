import { World } from '@/types/world.types'
import { WorldCard } from './WorldCard'

interface WorldsListProps {
  worlds: World[]
  isLoading: boolean
}

export function WorldsList({ worlds, isLoading }: WorldsListProps) {
  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="loading-dots">
          <div></div>
          <div></div>
          <div></div>
        </div>
        <p className="mt-3 text-text-secondary">Loading your worlds...</p>
      </div>
    )
  }

  if (!worlds || worlds.length === 0) {
    return (
      <div className="text-center py-8 text-text-secondary">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-3 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p>You haven't created any worlds yet.</p>
        <p className="text-sm mt-1">Fill out the form to begin your journey!</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {worlds.map((world) => (
        <WorldCard key={world.id} world={world} />
      ))}
    </div>
  )
}
